@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Manrope:wght@600;700&display=swap");
@import "tailwindcss";
@import "tw-animate-css";
@custom-variant dark (&:is(.dark *));
@theme {
--color-background: #f3efe7;
--color-foreground: #1b1b1b;
--color-card: #ffffff;
--color-card-foreground: #1b1b1b;
--color-popover: #ffffff;
--color-popover-foreground: #1b1b1b;
--color-primary: #1a1a1a;
--color-primary-foreground: #ffffff;
--color-secondary: #f5f0e6;
--color-secondary-foreground: #1b1b1b;
--color-muted: #f3eee4;
--color-muted-foreground: #8a847b;
--color-accent: #e7ff3a;
--color-accent-foreground: #1b1b1b;
--color-destructive: #ef4444;
--color-destructive-foreground: #ffffff;
--color-border: #e6dfd2;
--color-input: #e6dfd2;
--color-ring: #1a1a1a;
--color-chart-1: #e7ff3a;
--color-chart-2: #2f2f2f;
--color-chart-3: #ffd88a;
--color-chart-4: #b7b1a6;
--color-chart-5: #9e9a92;
--color-sidebar: #f5f0e6;
--color-sidebar-foreground: #5c5851;
--color-sidebar-primary: #1a1a1a;
--color-sidebar-primary-foreground: #ffffff;
--color-sidebar-accent: #f0eadf;
--color-sidebar-accent-foreground: #1a1a1a;
--color-sidebar-border: #e0d7c7;
--color-sidebar-ring: #1a1a1a;
--radius-lg: 1rem;
--radius-md: calc(1rem - 2px);
--radius-sm: calc(1rem - 4px);
--animate-accordion-down: accordion-down 0.2s ease-out;
@keyframes accordion-down {
from { height: 0; }
to { height: var(--radix-accordion-content-height); }
}
--animate-accordion-up: accordion-up 0.2s ease-out;
@keyframes accordion-up {
from { height: var(--radix-accordion-content-height); }
to { height: 0; }
}
--color-bg-gradient-start: #f4efe7;
--color-bg-gradient-end: #ece3cf;
--font-sans: "Inter", ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
--font-heading: "Manrope", ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
/*
The default border color has changed to currentColor in Tailwind CSS v4,
so we've added these compatibility styles to make sure everything still
looks the same as it did with Tailwind CSS v3.
If we ever want to remove these styles, we need to add an explicit border
color utility to any element that depends on these defaults.
*/
@layer base {
*,
::after,
::before,
::backdrop,
::file-selector-button {
border-color: var(--color-border);
}
}
@layer utilities {
body {
font-family: var(--font-sans);
}
}
@layer base {
:root {
--background: var(--color-background);
--foreground: var(--color-foreground);
--card: var(--color-card);
--card-foreground: var(--color-card-foreground);
--popover: var(--color-popover);
--popover-foreground: var(--color-popover-foreground);
--primary: var(--color-primary);
--primary-foreground: var(--color-primary-foreground);
--secondary: var(--color-secondary);
--secondary-foreground: var(--color-secondary-foreground);
--muted: var(--color-muted);
--muted-foreground: var(--color-muted-foreground);
--accent: var(--color-accent);
--accent-foreground: var(--color-accent-foreground);
--destructive: var(--color-destructive);
--destructive-foreground: var(--color-destructive-foreground);
--border: var(--color-border);
--input: var(--color-input);
--ring: var(--color-ring);
--chart-1: var(--color-chart-1);
--chart-2: var(--color-chart-2);
--chart-3: var(--color-chart-3);
--chart-4: var(--color-chart-4);
--chart-5: var(--color-chart-5);
--radius: var(--radius-lg);
--sidebar-background: var(--color-sidebar);
--sidebar-foreground: var(--color-sidebar-foreground);
--sidebar-primary: var(--color-sidebar-primary);
--sidebar-primary-foreground: var(--color-sidebar-primary-foreground);
--sidebar-accent: var(--color-sidebar-accent);
--sidebar-accent-foreground: var(--color-sidebar-accent-foreground);
--sidebar-border: var(--color-sidebar-border);
--sidebar-ring: var(--color-sidebar-ring);
--bg-gradient-start: var(--color-bg-gradient-start);
--bg-gradient-end: var(--color-bg-gradient-end);
}
}
@layer base {
body {
@apply bg-background text-foreground;
}
.container {
margin-inline: auto;
padding-inline: 2rem;
}
}
@layer base {
html {
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
text-rendering: optimizeLegibility;
}
body {
background-color: var(--color-background);
background-image: linear-gradient(180deg, var(--color-bg-gradient-start), var(--color-bg-gradient-end));
color: var(--color-foreground);
}
h1, h2, h3, h4 {
font-family: var(--font-heading);
color: var(--color-foreground);
}
h1 { font-weight: 700; letter-spacing: -0.02em; }
h2 { font-weight: 700; letter-spacing: -0.015em; }
h3 { font-weight: 600; letter-spacing: -0.01em; }
p { color: var(--color-foreground); }
small, .text-muted-foreground { color: var(--color-muted-foreground); }
a { color: var(--color-primary); text-decoration: none; }
.font-sans { font-family: var(--font-sans); }
.font-heading { font-family: var(--font-heading); }

/* Custom Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Thin scrollbar variant */
.custom-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
}

.custom-scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.2);
  border-radius: 2px;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.4);
}
}